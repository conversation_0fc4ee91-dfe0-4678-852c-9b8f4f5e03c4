<?php

namespace Tests\Unit;

use Botble\Ecommerce\Services\ElasticsearchService;
use Illuminate\Http\Request;
use Tests\TestCase;

class ElasticsearchServiceTest extends TestCase
{
    protected ElasticsearchService $elasticsearchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->elasticsearchService = app(ElasticsearchService::class);
    }

    /** @test */
    public function it_can_be_instantiated()
    {
        $this->assertInstanceOf(ElasticsearchService::class, $this->elasticsearchService);
    }

    /** @test */
    public function it_validates_search_parameters()
    {
        // Test with valid parameters
        $request = new Request([
            'q' => 'test query',
            'per_page' => 20,
            'page' => 1,
            'sort_by' => 'relevance',
            'min_price' => 10,
            'max_price' => 100,
            'brands' => [1, 2, 3],
            'categories' => [1, 2],
            'tags' => [1],
            'in_stock' => true,
            'featured' => false,
        ]);

        // This should not throw any exceptions
        $this->assertInstanceOf(Request::class, $request);
        $this->assertEquals('test query', $request->get('q'));
        $this->assertEquals(20, $request->get('per_page'));
        $this->assertEquals(1, $request->get('page'));
        $this->assertEquals('relevance', $request->get('sort_by'));
        $this->assertEquals(10, $request->get('min_price'));
        $this->assertEquals(100, $request->get('max_price'));
        $this->assertEquals([1, 2, 3], $request->get('brands'));
        $this->assertEquals([1, 2], $request->get('categories'));
        $this->assertEquals([1], $request->get('tags'));
        $this->assertTrue($request->get('in_stock'));
        $this->assertFalse($request->get('featured'));
    }

    /** @test */
    public function it_handles_empty_search_query()
    {
        $request = new Request(['q' => '', 'per_page' => 10]);
        
        // Should handle empty query gracefully
        $this->assertEquals('', $request->get('q'));
        $this->assertEquals(10, $request->get('per_page'));
    }

    /** @test */
    public function it_handles_null_search_query()
    {
        $request = new Request(['per_page' => 10]);
        
        // Should handle null query gracefully
        $this->assertNull($request->get('q'));
        $this->assertEquals(10, $request->get('per_page'));
    }

    /** @test */
    public function it_handles_pagination_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'page' => 2,
            'per_page' => 25,
        ]);

        $this->assertEquals(2, $request->get('page'));
        $this->assertEquals(25, $request->get('per_page'));
    }

    /** @test */
    public function it_handles_sorting_parameters()
    {
        $validSortOptions = [
            'relevance',
            'price_asc',
            'price_desc',
            'name_asc',
            'name_desc',
            'created_at_asc',
            'created_at_desc',
            'featured',
        ];

        foreach ($validSortOptions as $sortOption) {
            $request = new Request([
                'q' => 'test',
                'sort_by' => $sortOption,
            ]);

            $this->assertEquals($sortOption, $request->get('sort_by'));
        }
    }

    /** @test */
    public function it_handles_price_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'min_price' => 50.99,
            'max_price' => 199.99,
        ]);

        $this->assertEquals(50.99, $request->get('min_price'));
        $this->assertEquals(199.99, $request->get('max_price'));
    }

    /** @test */
    public function it_handles_brand_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'brands' => [1, 2, 3, 4, 5],
        ]);

        $this->assertEquals([1, 2, 3, 4, 5], $request->get('brands'));
        $this->assertIsArray($request->get('brands'));
    }

    /** @test */
    public function it_handles_category_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'categories' => [10, 20, 30],
        ]);

        $this->assertEquals([10, 20, 30], $request->get('categories'));
        $this->assertIsArray($request->get('categories'));
    }

    /** @test */
    public function it_handles_tag_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'tags' => [5, 10, 15],
        ]);

        $this->assertEquals([5, 10, 15], $request->get('tags'));
        $this->assertIsArray($request->get('tags'));
    }

    /** @test */
    public function it_handles_stock_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'in_stock' => true,
        ]);

        $this->assertTrue($request->get('in_stock'));

        $request = new Request([
            'q' => 'test',
            'in_stock' => false,
        ]);

        $this->assertFalse($request->get('in_stock'));

        $request = new Request([
            'q' => 'test',
            'in_stock' => 'true',
        ]);

        $this->assertEquals('true', $request->get('in_stock'));
    }

    /** @test */
    public function it_handles_featured_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'featured' => true,
        ]);

        $this->assertTrue($request->get('featured'));

        $request = new Request([
            'q' => 'test',
            'featured' => false,
        ]);

        $this->assertFalse($request->get('featured'));
    }

    /** @test */
    public function it_handles_attribute_filter_parameters()
    {
        $request = new Request([
            'q' => 'test',
            'attributes' => [
                'color' => ['red', 'blue'],
                'size' => ['large', 'medium'],
            ],
        ]);

        $attributes = $request->get('attributes');
        $this->assertIsArray($attributes);
        $this->assertArrayHasKey('color', $attributes);
        $this->assertArrayHasKey('size', $attributes);
        $this->assertEquals(['red', 'blue'], $attributes['color']);
        $this->assertEquals(['large', 'medium'], $attributes['size']);
    }

    /** @test */
    public function it_handles_suggestion_limit_parameter()
    {
        $limits = [1, 5, 10, 20];

        foreach ($limits as $limit) {
            $suggestions = $this->elasticsearchService->getSearchSuggestions('test', $limit);
            $this->assertIsArray($suggestions);
            // Note: We can't test the actual count without Elasticsearch running
            // but we can test that the method accepts the parameter
        }
    }

    /** @test */
    public function it_handles_empty_suggestion_query()
    {
        $suggestions = $this->elasticsearchService->getSearchSuggestions('', 5);
        $this->assertIsArray($suggestions);
    }

    /** @test */
    public function it_handles_complex_search_request()
    {
        $request = new Request([
            'q' => 'smartphone',
            'page' => 2,
            'per_page' => 20,
            'sort_by' => 'price_asc',
            'min_price' => 100,
            'max_price' => 800,
            'brands' => [1, 2, 3],
            'categories' => [10, 11],
            'tags' => [5, 6, 7],
            'attributes' => [
                'color' => ['black', 'white'],
                'storage' => ['128GB', '256GB'],
            ],
            'in_stock' => true,
            'featured' => false,
        ]);

        // Verify all parameters are correctly set
        $this->assertEquals('smartphone', $request->get('q'));
        $this->assertEquals(2, $request->get('page'));
        $this->assertEquals(20, $request->get('per_page'));
        $this->assertEquals('price_asc', $request->get('sort_by'));
        $this->assertEquals(100, $request->get('min_price'));
        $this->assertEquals(800, $request->get('max_price'));
        $this->assertEquals([1, 2, 3], $request->get('brands'));
        $this->assertEquals([10, 11], $request->get('categories'));
        $this->assertEquals([5, 6, 7], $request->get('tags'));
        $this->assertTrue($request->get('in_stock'));
        $this->assertFalse($request->get('featured'));

        $attributes = $request->get('attributes');
        $this->assertIsArray($attributes);
        $this->assertEquals(['black', 'white'], $attributes['color']);
        $this->assertEquals(['128GB', '256GB'], $attributes['storage']);
    }
}
