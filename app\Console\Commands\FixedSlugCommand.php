<?php

namespace App\Console\Commands;

use Botble\Ecommerce\Models\Product;
use Botble\Slug\Models\Slug;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class FixedSlugCommand extends Command
{
    protected $signature = 'fixed:slug-product';
    protected $description = 'Fix slug and product names safely';

    public function handle()
    {
        // Disable max execution time
        set_time_limit(0);

        Product::query()->chunk(500, function ($products) {
            foreach ($products as $product) {

                // If name is empty, get it from slug
                if (empty($product->name)) {
                    $slugModel = Slug::where('reference_id', $product->id)
                        ->where('reference_type', Product::class)
                        ->first();

                    if ($slugModel) {
                        $name = str_replace('-', ' ', $slugModel->key);
                        $product->name = Str::limit($name, 255, ''); // ensure DB column length
                        $product->saveQuietly();
                        $this->info("Updated product #{$product->id} name from slug");
                    }
                } else {
                    // Make sure slug exists and is short
                    $slugKey = Str::slug($product->name);
                    $slugKey = Str::limit($slugKey, 190, ''); // limit for slug column

                    Slug::updateOrCreate(
                        [
                            'reference_id' => $product->id,
                            'reference_type' => Product::class
                        ],
                        [
                            'key' => $slugKey,
                            'prefix' => 'products'
                        ]
                    );

                    $this->info("Product #{$product->id} slug fixed");
                }
            }
        });
    }
}
