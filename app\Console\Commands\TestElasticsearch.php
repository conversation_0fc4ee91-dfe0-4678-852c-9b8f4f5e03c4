<?php

namespace App\Console\Commands;

use Elastic\Elasticsearch\Client as ElasticsearchClient;
use Illuminate\Console\Command;
use Elasticsearch\Client;

class TestElasticsearch extends Command
{
    protected $signature = 'elasticsearch:test';
    protected $description = 'Test Elasticsearch connection';

    public function handle(ElasticsearchClient $client)
    {
        try {
            $info = $client->info();
            $this->info('✓ Successfully connected to Elasticsearch!');
            $this->info('Cluster: ' . $info['cluster_name']);
            $this->info('Version: ' . $info['version']['number']);
            
            // Test index existence
            $indexName = config('scout.elasticsearch.index', 'shofy_search') . '_products';
            $exists = $client->indices()->exists(['index' => $indexName]);
            
            if ($exists) {
                $this->info("✓ Index '{$indexName}' exists");
            } else {
                $this->warn("✗ Index '{$indexName}' does not exist");
                $this->info('Creating index...');
                $this->call('scout:index', ['name' => 'products']);
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error('✗ Failed to connect to Elasticsearch');
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
?>