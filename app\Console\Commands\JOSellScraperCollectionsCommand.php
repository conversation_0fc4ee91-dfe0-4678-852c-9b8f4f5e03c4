<?php

namespace App\Console\Commands;

use Botble\Ecommerce\Models\ProductCategory;
use Bo<PERSON>ble\Slug\Models\Slug;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class JOSellScraperCollectionsCommand extends Command
{
    protected $signature = 'import:jo-sell-collections {--page=1 : Starting page} {--max-pages=100 : Maximum pages to import}';
    protected $description = 'Import collections from Shopify store with Arabic and English support';

    protected string $baseUrl = 'https://jo-cell.com';
    protected int $perPage = 250;
    protected int $currentPage;
    protected int $maxPages;

    public function handle(): int
    {
        $this->currentPage = (int) $this->option('page');
        $this->maxPages = (int) $this->option('max-pages');
        $importedCount = 0;
        $round = 0;

        $this->info("Starting import collections from: {$this->baseUrl}");
        $this->info("Page range: {$this->currentPage} to {$this->maxPages}");

        do {
            try {
                $response = $this->fetchCollectionsPage($this->currentPage);

                if ($response->failed()) {
                    $this->error("Failed to fetch page {$this->currentPage}: HTTP {$response->status()}");
                    break;
                }

                $data = $response->json();
                $round++;

                if (empty($data['collections'])) {
                    $this->info("No more collections found on page {$this->currentPage}");
                    break;
                }

                $this->info("Processing page {$this->currentPage} with " . count($data['collections']) . " collections");

                DB::transaction(function () use ($data, &$importedCount) {
                    foreach ($data['collections'] as $collectionData) {
                        try {
                            $this->processCollection($collectionData);
                            $importedCount++;
                        } catch (Exception $e) {
                            $this->error("Failed to process collection {$collectionData['id']}: {$e->getMessage()}");
                        }
                    }
                });

                $this->info("Successfully processed page {$this->currentPage}");
                $hasMoreCollections = count($data['collections']) === $this->perPage;
                $this->currentPage++;

            } catch (Exception $e) {
                $this->error("Error processing page {$this->currentPage}: {$e->getMessage()}");
                break;
            }

        } while (
            count($data['collections']) === $this->perPage &&
            (!$this->maxPages || $this->currentPage <= $this->maxPages) &&
            $round >= 5
        );

        $this->info("Import completed. Total collections imported: {$importedCount}");
        return 0;
    }

    protected function fetchCollectionsPage(int $page)
    {
        $apiUrl = "{$this->baseUrl}/collections.json?limit={$this->perPage}&page={$page}";
        $this->info("Fetching collections from: {$apiUrl}");

        return Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'Cookie' => 'shopify_test_cookie=1;',
        ])
            ->timeout(30)
            ->retry(3, 1000)
            ->get($apiUrl);
    }

    protected function fetchCollectionByHandle(string $handle, string $locale = 'ar')
    {
        if ($locale == 'ar') {
            $apiUrl = "{$this->baseUrl}/{$locale}/collections/{$handle}.json";
        } else {
            $apiUrl = "{$this->baseUrl}/collections/{$handle}.json";
        }

        $this->info("Fetching {$locale} version for {$handle} from: {$apiUrl}");

        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])
                ->timeout(30)
                ->retry(3, 1000)
                ->get($apiUrl);

            if ($response->successful()) {
                return $response->json('collection');
            }
        } catch (Exception $e) {
            $this->warn("Failed to fetch {$locale} version for {$handle}: {$e->getMessage()}");
        }

        return null;
    }

    protected function processCollection(array $collectionData): void
    {
        $isArabic = $this->isArabicText($collectionData['title']);
        $arabicData = null;
        $englishData = null;

        if ($isArabic) {
            $arabicData = $collectionData;
            $englishData = $this->fetchCollectionByHandle($collectionData['handle'], 'en');
        } else {
            $englishData = $collectionData;
            $arabicData = $this->fetchCollectionByHandle($collectionData['handle'], 'ar');
        }

        $this->createOrUpdateCollection($arabicData, $englishData);
    }

    protected function createOrUpdateCollection(?array $arabicData, ?array $englishData): ?ProductCategory
    {
        $primaryData = $arabicData ?? $englishData;

        if (!$primaryData) {
            $this->warn("No collection data available");
            return null;
        }

        $category = ProductCategory::updateOrCreate(
            ['shopify_id' => $primaryData['id']],
            [
                'name' => $arabicData['title'] ?? $englishData['title'] ?? 'Untitled',
                'description' => $this->sanitizeHtml($arabicData['body_html'] ?? ''),
                'status' => 'published',
                'is_default' => false,
                'is_featured' => $primaryData['featured_image'] ? true : false,
                'order' => 0,
                'author_id' => 0,
                'author_type' => 'Botble\ACL\Models\User',
            ]
        );

        // Save English translation if available
        if ($englishData && $englishData['title']) {
            DB::table('ec_product_categories_translations')->updateOrInsert(
                [
                    'lang_code' => 'en_US',
                    'ec_product_categories_id' => $category->id,
                ],
                [
                    'name' => $englishData['title'],
                    'description' => $this->sanitizeHtml($englishData['body_html'] ?? ''),
                ]
            );
        }

        // Save Arabic translation if available and primary is English
        if ($englishData && $arabicData && $englishData['title']) {
            DB::table('ec_product_categories_translations')->updateOrInsert(
                [
                    'lang_code' => 'ar',
                    'ec_product_categories_id' => $category->id,
                ],
                [
                    'name' => $arabicData['title'],
                    'description' => $this->sanitizeHtml($arabicData['body_html'] ?? ''),
                ]
            );
        }

        // Create slug
        $slugTitle = $englishData['title'] ?? $arabicData['title'] ?? $category->name;
        $this->createSlug($category, $slugTitle);

        return $category;
    }

    protected function createSlug(ProductCategory $category, string $title): void
    {
        Slug::updateOrCreate(
            [
                'reference_id' => $category->id,
                'reference_type' => ProductCategory::class,
            ],
            [
                'key' => Str::slug($title),
                'prefix' => 'categories',
            ]
        );
    }

    protected function isArabicText(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) === 1;
    }

    protected function sanitizeHtml(string $html): string
    {
        // Remove script tags and their content
        $html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $html);
        
        // Remove style tags and their content
        $html = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $html);
        
        // Keep basic formatting but remove potentially dangerous attributes
        $allowed = '<p><br><strong><em><u><h1><h2><h3><h4><h5><h6><ul><ol><li><img><a>';
        $html = strip_tags($html, $allowed);
        
        return trim($html);
    }
}