<?php

namespace Bo<PERSON>ble\Ecommerce\Console\Commands;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductVariation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class IndexProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ecommerce:index-products 
                            {--chunk=100 : Number of products to process at once}
                            {--force : Force reindex all products}
                            {--variations : Also index product variations}
                            {--clear : Clear existing index before indexing}';

    /**
     * The console command description.
     */
    protected $description = 'Index products and variations into Elasticsearch for search functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting product indexing...');

        $chunkSize = (int) $this->option('chunk');
        $force = $this->option('force');
        $indexVariations = $this->option('variations');
        $clearIndex = $this->option('clear');

        try {
            // Clear existing index if requested
            if ($clearIndex) {
                $this->clearIndex();
            }

            // Index products
            $this->indexProducts($chunkSize, $force);

            // Index variations if requested
            if ($indexVariations) {
                $this->indexProductVariations($chunkSize, $force);
            }

            $this->info('Product indexing completed successfully!');
            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Error during indexing: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * Clear existing search index
     */
    protected function clearIndex(): void
    {
        $this->info('Clearing existing search index...');

        try {
            // Clear products index
            Product::removeAllFromSearch();
            $this->info('Products index cleared.');

            // Clear variations index
            ProductVariation::removeAllFromSearch();
            $this->info('Product variations index cleared.');

        } catch (\Exception $e) {
            $this->warn('Could not clear index: ' . $e->getMessage());
        }
    }

    /**
     * Index products into Elasticsearch
     */
    protected function indexProducts(int $chunkSize, bool $force): void
    {
        $this->info('Indexing products...');

        $query = Product::query()
            ->with(['brand', 'categories', 'tags', 'variationAttributeSwatchesForProductList'])
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->where('is_variation', false);

        $totalProducts = $query->count();
        $this->info("Found {$totalProducts} products to index.");

        if ($totalProducts === 0) {
            $this->warn('No products found to index.');
            return;
        }

        $progressBar = $this->output->createProgressBar($totalProducts);
        $progressBar->start();

        $indexed = 0;
        $errors = 0;

        $query->chunk($chunkSize, function ($products) use (&$indexed, &$errors, $progressBar) {
            $productIds = [];

            foreach ($products as $product) {
                try {
                    $productIds[] = $product->id;
                    $indexed++;
                } catch (\Exception $e) {
                    $errors++;
                    $this->warn("\nError indexing product {$product->id}: " . $e->getMessage());
                }
                $progressBar->advance();
            }

            // Batch index products
            if (!empty($productIds)) {
                try {
                    Product::whereIn('id', $productIds)->searchable();
                } catch (\Exception $e) {
                    $this->warn("\nError batch indexing products: " . $e->getMessage());
                    $errors += count($productIds);
                }
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info("Products indexing completed. Indexed: {$indexed}, Errors: {$errors}");
    }

    /**
     * Index product variations into Elasticsearch
     */
    protected function indexProductVariations(int $chunkSize, bool $force): void
    {
        $this->info('Indexing product variations...');

        $query = ProductVariation::query()
            ->with(['product', 'configurableProduct', 'productAttributes'])
            ->whereHas('product', function ($query) {
                $query->where('status', BaseStatusEnum::PUBLISHED)
                      ->where('is_variation', true);
            });

        $totalVariations = $query->count();
        $this->info("Found {$totalVariations} product variations to index.");

        if ($totalVariations === 0) {
            $this->warn('No product variations found to index.');
            return;
        }

        $progressBar = $this->output->createProgressBar($totalVariations);
        $progressBar->start();

        $indexed = 0;
        $errors = 0;

        $query->chunk($chunkSize, function ($variations) use (&$indexed, &$errors, $progressBar) {
            $variationIds = [];

            foreach ($variations as $variation) {
                try {
                    $variationIds[] = $variation->id;
                    $indexed++;
                } catch (\Exception $e) {
                    $errors++;
                    $this->warn("\nError indexing variation {$variation->id}: " . $e->getMessage());
                }
                $progressBar->advance();
            }

            // Batch index variations
            if (!empty($variationIds)) {
                try {
                    ProductVariation::whereIn('id', $variationIds)->searchable();
                } catch (\Exception $e) {
                    $this->warn("\nError batch indexing variations: " . $e->getMessage());
                    $errors += count($variationIds);
                }
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info("Variations indexing completed. Indexed: {$indexed}, Errors: {$errors}");
    }

    /**
     * Get indexing statistics
     */
    protected function getIndexingStats(): array
    {
        try {
            $productCount = Product::search('*')->count();
            $variationCount = ProductVariation::search('*')->count();

            return [
                'products' => $productCount,
                'variations' => $variationCount,
                'total' => $productCount + $variationCount,
            ];
        } catch (\Exception $e) {
            return [
                'products' => 'Error',
                'variations' => 'Error',
                'total' => 'Error',
                'error' => $e->getMessage(),
            ];
        }
    }
}
