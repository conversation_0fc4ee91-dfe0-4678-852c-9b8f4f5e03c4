<?php

namespace App\Console\Commands;

use Botble\Blog\Models\Category;
use Botble\Blog\Models\Tag;
use <PERSON><PERSON>ble\Ecommerce\Enums\ProductTypeEnum;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductAttribute;
use Botble\Ecommerce\Models\ProductAttributeSet;
use Botble\Ecommerce\Models\ProductVariation;
use Botble\Ecommerce\Models\ProductVariationItem;
use Botble\Media\Facades\RvMedia;
use Botble\Media\Models\MediaFile;
use Botble\Media\Models\MediaFolder;
use Botble\Slug\Models\Slug;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportShopifyProduct extends Command
{
    protected $signature = 'import:shopify-product
                           {--url= : Shopify store URL}';

    protected $description = 'Import products from a Shopify store via API';

    public function handle()
    {
        $shopifyUrl = $this->option('url');


        if (!$shopifyUrl) {
            $this->error('Please provide a Shopify store URL using --url option.');
            return 1;
        } else {

            try {
                $response = $this->fetchProduct($shopifyUrl);

                if ($response->failed()) {
                    $this->error("Failed to fetch products: HTTP {$response->status()}");
                    return 1;
                }

                $data = $response->json();

                if (empty($data['product'])) {
                    $this->info("No more products found on page {$shopifyUrl}");
                    return 0;
                }


                DB::transaction(function () use ($data) {
                    $this->processProduct($data['product']);
                });
            } catch (\Exception $e) {
                $this->error("Error processing page {$shopifyUrl}: " . $e->getMessage());
                return 0;
            }


            $this->info("Importing products from: {$shopifyUrl}");
        }


        return 0;
    }

    protected function fetchProduct(string $shopifyUrl)
    {

        return Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'Cookie' => 'shopify_test_cookie=1;',
        ])
            ->timeout(30)
            ->retry(3, 1000)
            ->get($shopifyUrl);
    }


    protected function processProduct(array $productData)
    {
        // Create or update main product
        $mainProduct = $this->createMainProduct($productData);

        // Process product attributes and variations
        if (!empty($productData['options'])) {
            $this->processProductAttributes($productData, $mainProduct);
        }

        // Process variants if they exist
        if (!empty($productData['variants'])) {
            $this->processProductVariants($productData, $mainProduct);
        }

        $this->processProductImages($productData, $mainProduct);

        $this->asignImageToVariations($productData);
        $this->processTags($productData, $mainProduct);

        //$this->processVendor($productData, $mainProduct);

        $this->processProductType($productData, $mainProduct); //producttType 

    }

    protected function createMainProduct(array $productData): Product
    {
        $Product = Product::updateOrCreate(
            ['shopify_id' => $productData['id']],
            [
                'name' => $productData['title'],
                'description' => $productData['body_html'] ?? '',
                'content' => $productData['body_html'] ?? '',
                'status' => $productData['status'] ?? 'published',
                'is_featured' => $productData['is_featured'] ?? false,
                'price' => $this->parsePrice($productData['variants'][0]['price'] ?? 0),
                // 'sale_price' => $this->parsePrice($productData['variants'][0]['compare_at_price'] ?? null),
                'sku' => $productData['variants'][0]['sku'] ?? null,
                'barcode' => $productData['variants'][0]['barcode'] ?? null,
                'quantity' => $productData['variants'][0]['inventory_quantity'] ?? 0,
                'weight' => $productData['variants'][0]['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => 0,
                'with_storehouse_management' => true,
                'stock_status' => 'in_stock',
                // 'stock_status' => ($productData['variants'][0]['inventory_quantity'] ?? 0) > 0
                //     ? 'in_stock'
                //     : 'out_of_stock',
            ]
        );


        $slug = Slug::updateOrCreate([
            'reference_id' => $Product->id,
            'reference_type' => Product::class,
        ], [
            'key' => Str::slug($Product->name),
            'prefix' => 'products',
            'reference_id' => $Product->id,
            'reference_type' => Product::class,

        ]);
        return $Product;
    }

    protected function processProductAttributes(array $productData, Product $mainProduct)
    {
        foreach ($productData['options'] as $option) {
            $attributeSet = ProductAttributeSet::updateOrCreate(
                ['title' => $option['name']],
                [
                    'title' => $option['name'],
                    'slug' => Str::slug($option['name']),
                    'status' => 'published',
                    'order' => 0,
                    'display_layout' => "text",
                    'is_searchable' => true,
                    'is_comparable' => true,
                    'is_use_in_product_listing' => true,
                    'use_image_from_product_variation' => true,
                ]
            );

            // Attach attribute set to product
            $mainProduct->productAttributeSets()->syncWithoutDetaching([$attributeSet->id]);

            // Create attributes for each option value
            foreach ($option['values'] as $value) {
                $attribute = ProductAttribute::updateOrCreate(
                    [
                        'title' => $value,
                        'attribute_set_id' => $attributeSet->id,
                    ],
                    [
                        'title' => $value,
                        'slug' => Str::slug($value),
                        'attribute_set_id' => $attributeSet->id,
                        'order' => 0,
                    ]
                );
            }
        }
    }

    protected function processProductVariants(array $productData, Product $mainProduct)
    {
        $variants = [];


        // // Create product variations if we have multiple variants
        ProductVariation::where('configurable_product_id', $mainProduct->id)->delete();

        if (count($productData['variants']) > 1) {
            foreach ($productData['variants'] as $index => $variant) {


                $variantProduct = $this->createVariantProduct($productData, $variant, $mainProduct);

                $variantModel = ProductVariation::create([
                    'configurable_product_id' => $mainProduct->id,
                    'product_id' => $variantProduct->id,
                    'is_default' => $index === 0,
                ]);
                $this->info("variantModel {$variantProduct->id} created");

                $this->processVariantAttributes($mainProduct, $variantProduct, $variantModel, $variant);
            }



            $this->info("Product variations created for {$mainProduct->name}");
        }
    }

    protected function createVariantProduct(array $productData, array $variant, Product $mainProduct): Product
    {
        $this->info('prtice variant: ' . $this->parsePrice($variant['price'] ?? 0));
        $Product = Product::updateOrCreate(
            ['shopify_id' => $variant['id']],
            [
                'name' => $productData['title'],
                'description' => '',
                'status' => 'published',
                'price' => $this->parsePrice($variant['price'] ?? 0),
                // 'sale_price' => $this->parsePrice($variant['compare_at_price'] ?? null),
                'sku' => $variant['sku'] ?? null,
                'barcode' => $variant['barcode'] ?? null,
                'quantity' => $variant['inventory_quantity'] ?? 0,
                'weight' => $variant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => true,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
                // 'stock_status' => ($variant['inventory_quantity'] ?? 0) > 0
                //     ? 'in_stock'
                //     : 'out_of_stock',
            ]
        );

        $slug = Slug::updateOrCreate([
            'reference_id' => $Product->id,
            'reference_type' => Product::class,
        ], [
            'key' => Str::slug($Product->name),
            'prefix' => 'products',
            'reference_id' => $Product->id,
            'reference_type' => Product::class,

        ]);

        $this->info("Product {$Product->name} created");

        return $Product;
    }

    protected function processVariantAttributes($mainProduct, $variantProduct, $variantModel, $variant)
    {
        $attributesToAttach = [];

        // Process each variant option
        for ($i = 1; $i <= 3; $i++) {
            $optionKey = "option{$i}";

            if (empty($variant[$optionKey])) {
                continue;
            }

            $optionValue = $variant[$optionKey];

            // Find matching attribute
            $attribute = ProductAttribute::where('title', $optionValue)->first();

            if (!$attribute) {

                $attribute = ProductAttribute::create([
                    'title' => $optionValue,
                    'attribute_set_id' => $mainProduct->attribute_set_id,
                ]);
            }

            $this->info("optionValue: {$optionValue} , Attribute  OPTION ID: {$attribute->id}, option{$i} ,'variantModel' : {$variantModel->id}  , 'variantProduct'  : {$variantProduct->id} ,'variantProduct NAME' : {$variantProduct->name} ' , 'mainProduct' : {$mainProduct->id} ");
            ProductVariationItem::updateOrCreate(
                [
                    'variation_id' => $variantModel->id,
                    'attribute_id' => $attribute->id,
                ],
                [
                    'variation_id' => $variantModel->id,
                    'attribute_id' => $attribute->id,
                ]
            );
        }
    }




    protected function processProductImages(array $productData, Product $product)
    {
        if (empty($productData['images'])) {
            return;
        }

        $imageUrls = [];
        $folderId = $this->ensureProductImagesFolder($product);

        foreach ($productData['images'] as $image) {
            try {
                $mediaModel = MediaFile::where('shopify_id', $image['id'])->first();

                if (is_null($mediaModel)) {
                    $imageUrl = $image['src'];
                    $imageName = $this->generateImageName($imageUrl);
                    $uploadResult = $this->downloadAndStoreImage($imageUrl, $folderId, $imageName);

                    if ($uploadResult && $uploadResult['error'] === false) {
                        $fileData = $uploadResult['data'];
                      

                        // Create media file record
                        $mediaModel = MediaFile::create([
                            'name' => $fileData['name'],
                            'mime_type' => $fileData['mime_type'],
                            'size' => $fileData['size'],
                            'url' => $fileData['url'],
                            'folder_id' => $folderId,
                            'shopify_id' => (string) $image['id'],
                            'user_id' => 0,
                        ]);


                        if (!$mediaModel) {
                            $this->error("Failed to insert MediaFile record");
                        } else {
                            $this->info("Image uploaded: {" . $fileData['url'] . "} mediaModel ID: {$mediaModel->id} ,mediaModel shopify_id: {$mediaModel->shopify_id} , image ID: " . $image['id']);
                        }
                    }
                }

                $imageUrls[] = $mediaModel->url;
            } catch (Exception $e) {
                $this->error("Failed to process image: {$e->getMessage()}");
                continue;
            }
        }

        if (!empty($imageUrls)) {
            $product->update([
                'images' => $imageUrls,
                'image' => $imageUrls[0] ?? null
            ]);
        }
    }
    protected function ensureProductImagesFolder(Product $product): int
    {
        // $folder = MediaFolder::firstOrCreate(
        //     ['name' => "$product->id"],
        //     [
        //         'parent_id' => 0,
        //         'slug' => 'shopify-products',
        //         'user_id' => 0,
        //     ]
        // );

        $folder = MediaFolder::updateOrCreate(
            ['name' => "$product->id"],
            [
            'name' => "$product->id",
               'parent_id' => 10,
                'slug' => 'shopify-products',
                'user_id' => 0,
            ]
        );

        return $folder->id;
    }

    protected function downloadAndStoreImage(string $imageUrl, int $folderId, string $imageName): ?array
    {
        try {
            // Set a strict timeout (30 seconds as per your requirement)
            set_time_limit(30);

            // Create tmp directory if it doesn't exist
            $tmpDir = storage_path('app/public/tmp');
            if (!file_exists($tmpDir)) {
                mkdir($tmpDir, 0755, true);
            }

            // Download the image with strict timeout
            $response = Http::timeout(20) // Lower than max execution time
                ->connectTimeout(10)
                ->retry(2, 1000)
                ->get($imageUrl);

            if (!$response->successful()) {
                $this->warn("Failed to download image: {$imageUrl} - HTTP {$response->status()}");
                return null;
            }

            $imageData = $response->getBody()->getContents();

            // Clean the URL to get proper extension
            $cleanedUrl = preg_replace('/\?.*/', '', $imageUrl);
            $fileExtension = pathinfo($cleanedUrl, PATHINFO_EXTENSION) ?: 'jpg';
            $fileName = $imageName . '.' . $fileExtension;

            // Store in temporary location
            $storagePath = $tmpDir . '/' . $fileName;
            file_put_contents($storagePath, $imageData);

            // Import to media library with timeout protection
            try {
                $uploadResult = RvMedia::uploadFromPath(
                    $storagePath,
                    $folderId,
                    'products',
                    $fileName
                );
            } catch (\Exception $e) {
                $this->warn("Media library processing failed for {$imageUrl}: {$e->getMessage()}");
                return null;
            } finally {
                // Clean up temp file
                if (file_exists($storagePath)) {
                    unlink($storagePath);
                }
            }

            return $uploadResult;
        } catch (\Exception $e) {
            $this->warn("Image download failed: {$imageUrl} - {$e->getMessage()}");
            return null;
        }
    }

    protected function generateImageName(string $imageUrl): string
    {
        $baseName = pathinfo($imageUrl, PATHINFO_FILENAME);
        return Str::slug($baseName) . '-' . Str::random(5);
    }

    protected function parsePrice($price)
    {
        if (is_null($price)) {
            return null;
        }

        return (float) preg_replace('/[^0-9.]/', '', $price);
    }



    public function asignImageToVariations(array $productData)
    {
        if (empty($productData['variants'])) {
            return;
        }

        foreach ($productData['variants'] as $variant) {
            $variantProduct = Product::where('shopify_id', $variant['id'])->first();

            if (!$variantProduct) {
                continue;
            }

            if (isset($variant['image_id'])) {
                $media = MediaFile::where('shopify_id', $variant['image_id'])->first();
                if ($media) {
                    $variantProduct->update(['image' => $media->url]);
                    $this->info("Image assigned to variant: {$variantProduct->name}");
                } else {
                    $this->warn("No media found for image ID: {$variant['image_id']} in variant: {$variantProduct->name}");
                }
            }
        }
    }




    public function processTags(array $productData, Product $product)
    {
        if (isset($productData['tags'])) {
              if (!is_array($productData['tags'])) {
                    $tags = explode(',', $productData['tags']);
                } else {
                    $tags = array_map('trim', $productData['tags']);
                }
                $tagsId=[];
                foreach ($tags as $key => $tag) {
                    $tag = trim($tag);
                    $tagsId[] = Tag::updateOrCreate(
                        ['name' => $tag],
                        [
                            'name' => $tag,
                            'description' => '',
                            'status' => 'published',
                            'author_id' => 0,
                            'author_type' => 'system',
                        ]
                    )->id;
                    $this->info("Tag processed: {$tag}");
                }
                try {
                    $product->tags()->sync($tagsId);
                } catch (Exception $e) {
                    $this->error("Failed to detach existing tags: " . $e->getMessage());
                }
         
                return $tags;
        }
    }




     public function processVendor(array $productData, Product $product)
    {
        if (isset($productData['vendor'])) {
            $vendor = trim($productData['vendor']);
            if (!empty($vendor)) {
                $product->update(['vendor' => $vendor]);
                $this->info("Vendor processed: {$vendor}");
            } else {
                $this->warn("Vendor is empty for product: {$product->name}");
            }
        } else {
            $this->warn("No vendor found for product: {$product->name}");
        }
    }




    public function processProductType(array $productData, Product $product)
    {
        if (isset($productData['product_type'])) {
            $productType = trim($productData['product_type']);
            if (!empty($productType)) {
               
                $category = Category::updateOrCreate(
                    ['name' => $productType],
                    [
                        'name' => $productType,
                        'description' => '',
                        'status' => 'published',
                        'is_default' => false,
                    ]
                );
                $product->categories()->syncWithoutDetaching([$category->id]);

            } else {
                $this->warn("Product type is empty for product: {$product->name}");
            }
        } else {
            $this->warn("No product type found for product: {$product->name}");
        }
    }
}
