<?php

use Bo<PERSON><PERSON>\Ecommerce\Http\Controllers\API\ProductSearchController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes for Ecommerce
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the ecommerce plugin.
| These routes are loaded by the EcommerceServiceProvider and all of them
| will be assigned to the "api" middleware group.
|
*/

Route::group([
    'middleware' => ['api'],
    'prefix' => 'api/v1',
    'namespace' => 'Botble\Ecommerce\Http\Controllers\API',
], function () {
    
    // Product Search API Routes
    Route::prefix('products')->name('api.products.')->group(function () {
        Route::post('search', [ProductSearchController::class, 'searchProducts'])
            ->name('search');
        
        Route::post('search/variations', [ProductSearchController::class, 'searchVariations'])
            ->name('search.variations');
        
        Route::get('search/suggestions', [ProductSearchController::class, 'suggestions'])
            ->name('search.suggestions');
        
        Route::get('search/aggregations', [ProductSearchController::class, 'aggregations'])
            ->name('search.aggregations');
        
        Route::get('search/health', [ProductSearchController::class, 'healthCheck'])
            ->name('search.health');
    });
    
    // Alternative routes for backward compatibility
    Route::post('search/products', [ProductSearchController::class, 'searchProducts'])
        ->name('api.search.products');
    
    Route::post('search/variations', [ProductSearchController::class, 'searchVariations'])
        ->name('api.search.variations');
    
    Route::get('search/suggestions', [ProductSearchController::class, 'suggestions'])
        ->name('api.search.suggestions');
});
