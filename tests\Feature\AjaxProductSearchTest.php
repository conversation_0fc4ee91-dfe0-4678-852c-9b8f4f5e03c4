<?php

namespace Tests\Feature;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductCategory;
use Botble\Ecommerce\Models\Brand;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AjaxProductSearchTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestProducts();
    }

    protected function createTestProducts(): void
    {
        // Create a brand
        $brand = Brand::create([
            'name' => 'Test Brand',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        // Create a category
        $category = ProductCategory::create([
            'name' => 'Test Category',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        // Create test products
        Product::create([
            'name' => 'iPhone 14 Pro',
            'description' => 'Latest iPhone model',
            'content' => 'iPhone 14 Pro with advanced features',
            'sku' => 'IPHONE14PRO',
            'price' => 999.99,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
            'brand_id' => $brand->id,
        ]);

        Product::create([
            'name' => 'Samsung Galaxy S23',
            'description' => 'Latest Samsung smartphone',
            'content' => 'Samsung Galaxy S23 with amazing camera',
            'sku' => 'GALAXYS23',
            'price' => 899.99,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
            'brand_id' => $brand->id,
        ]);

        Product::create([
            'name' => 'MacBook Pro',
            'description' => 'Apple laptop',
            'content' => 'MacBook Pro for professionals',
            'sku' => 'MACBOOKPRO',
            'price' => 1999.99,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
            'brand_id' => $brand->id,
        ]);
    }

    public function  test_ajax_products_without_search_returns_all_products()
    {
        $response = $this->get('/ajax/products?limit=10');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'error',
            'data' => [
                'count',
                'html'
            ]
        ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['html']);
        $this->assertEquals('3', $data['count']);
    }

    public function test_ajax_products_with_search_query_filters_results()
    {
        $response = $this->get('/ajax/products?q=iPhone&limit=10');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'error',
            'data' => [
                'count',
                'html'
            ],
            'message'
        ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['html']);
        $this->assertEquals('1', $data['count']);
        $this->assertStringContains('iPhone', $data['html']);
    }

    public function test_ajax_products_with_search_query_returns_multiple_matches()
    {
        $response = $this->get('/ajax/products?q=Pro&limit=10');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertNotEmpty($data['html']);
        $this->assertEquals('2', $data['count']); // iPhone 14 Pro and MacBook Pro
    }

    public function test_ajax_products_with_no_matching_search_returns_empty()
    {
        $response = $this->get('/ajax/products?q=NonExistentProduct&limit=10');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertEquals('0', $data['count']);
    }

    public function test_ajax_products_with_arabic_search_query()
    {
        // Create a product with Arabic name
        Product::create([
            'name' => 'ايفون 15',
            'description' => 'احدث موبايل ايفون',
            'content' => 'ايفون 15 مع مميزات متقدمة',
            'sku' => 'IPHONE15AR',
            'price' => 1099.99,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $response = $this->get('/ajax/products?q=ايفون&limit=10');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertNotEmpty($data['html']);
        $this->assertEquals('1', $data['count']);
        $this->assertStringContains('ايفون', $data['html']);
    }

    public function test_ajax_products_with_featured_type()
    {
        $response = $this->get('/ajax/products?type=featured&limit=10');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'error',
            'data' => [
                'count',
                'html'
            ]
        ]);
    }

    public function test_ajax_products_with_limit_parameter()
    {
        $response = $this->get('/ajax/products?limit=2');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertNotEmpty($data['html']);
        // Should return maximum 2 products
        $this->assertLessThanOrEqual(2, (int)str_replace(',', '', $data['count']));
    }
}
