<?php

namespace Tests\Feature;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>ble\Ecommerce\Models\Brand;
use <PERSON><PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\Ecommerce\Models\ProductCategory;
use Botble\Ecommerce\Models\ProductTag;
use Bo<PERSON>ble\Ecommerce\Services\ElasticsearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class ElasticsearchProductSearchTest extends TestCase
{
    use RefreshDatabase;

    protected ElasticsearchService $elasticsearchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->elasticsearchService = app(ElasticsearchService::class);
        
        // Skip if Elasticsearch is not available
        if (!$this->isElasticsearchAvailable()) {
            $this->markTestSkipped('Elasticsearch is not available');
        }
    }

    protected function isElasticsearchAvailable(): bool
    {
        try {
            $request = new Request(['q' => '', 'per_page' => 1]);
            $this->elasticsearchService->searchProducts($request);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /** @test */
    public function it_can_search_products_by_name()
    {
        // Create test products
        $product1 = Product::factory()->create([
            'name' => 'iPhone 15 Pro',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $product2 = Product::factory()->create([
            'name' => 'Samsung Galaxy S24',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$product1->id, $product2->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search for iPhone
        $request = new Request(['q' => 'iPhone', 'per_page' => 10]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        $this->assertTrue($results->contains('id', $product1->id));
        $this->assertFalse($results->contains('id', $product2->id));
    }

    /** @test */
    public function it_can_filter_products_by_price_range()
    {
        // Create test products with different prices
        $cheapProduct = Product::factory()->create([
            'name' => 'Cheap Product',
            'price' => 50,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $expensiveProduct = Product::factory()->create([
            'name' => 'Expensive Product',
            'price' => 500,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$cheapProduct->id, $expensiveProduct->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search with price filter
        $request = new Request([
            'q' => '',
            'min_price' => 100,
            'max_price' => 600,
            'per_page' => 10
        ]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        $this->assertTrue($results->contains('id', $expensiveProduct->id));
        $this->assertFalse($results->contains('id', $cheapProduct->id));
    }

    /** @test */
    public function it_can_filter_products_by_brand()
    {
        // Create test brands
        $brand1 = Brand::factory()->create(['name' => 'Apple']);
        $brand2 = Brand::factory()->create(['name' => 'Samsung']);

        // Create test products
        $appleProduct = Product::factory()->create([
            'name' => 'iPhone',
            'brand_id' => $brand1->id,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $samsungProduct = Product::factory()->create([
            'name' => 'Galaxy',
            'brand_id' => $brand2->id,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$appleProduct->id, $samsungProduct->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search with brand filter
        $request = new Request([
            'q' => '',
            'brands' => [$brand1->id],
            'per_page' => 10
        ]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        $this->assertTrue($results->contains('id', $appleProduct->id));
        $this->assertFalse($results->contains('id', $samsungProduct->id));
    }

    /** @test */
    public function it_can_sort_products_by_price()
    {
        // Create test products with different prices
        $product1 = Product::factory()->create([
            'name' => 'Product A',
            'price' => 100,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $product2 = Product::factory()->create([
            'name' => 'Product B',
            'price' => 200,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $product3 = Product::factory()->create([
            'name' => 'Product C',
            'price' => 150,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$product1->id, $product2->id, $product3->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search with price ascending sort
        $request = new Request([
            'q' => '',
            'sort_by' => 'price_asc',
            'per_page' => 10
        ]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        
        // Check if results are sorted by price ascending
        $prices = $results->pluck('price')->toArray();
        $sortedPrices = collect($prices)->sort()->values()->toArray();
        $this->assertEquals($sortedPrices, $prices);
    }

    /** @test */
    public function it_can_get_search_suggestions()
    {
        // Create test product
        $product = Product::factory()->create([
            'name' => 'iPhone 15 Pro Max',
            'sku' => 'IPHONE15PM',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index product
        $product->searchable();

        // Wait for indexing
        sleep(1);

        // Get suggestions
        $suggestions = $this->elasticsearchService->getSearchSuggestions('iPhone', 5);

        $this->assertIsArray($suggestions);
        $this->assertGreaterThan(0, count($suggestions));
        
        $suggestion = collect($suggestions)->first();
        $this->assertArrayHasKey('id', $suggestion);
        $this->assertArrayHasKey('name', $suggestion);
        $this->assertArrayHasKey('sku', $suggestion);
        $this->assertArrayHasKey('price', $suggestion);
    }

    /** @test */
    public function it_only_searches_published_products()
    {
        // Create published and draft products
        $publishedProduct = Product::factory()->create([
            'name' => 'Published Product',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $draftProduct = Product::factory()->create([
            'name' => 'Draft Product',
            'status' => BaseStatusEnum::DRAFT,
            'is_variation' => false,
        ]);

        // Index products (only published should be indexed)
        Product::whereIn('id', [$publishedProduct->id, $draftProduct->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search for products
        $request = new Request(['q' => 'Product', 'per_page' => 10]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        $this->assertTrue($results->contains('id', $publishedProduct->id));
        $this->assertFalse($results->contains('id', $draftProduct->id));
    }

    /** @test */
    public function it_excludes_variation_products_from_main_search()
    {
        // Create main product and variation
        $mainProduct = Product::factory()->create([
            'name' => 'Main Product',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $variationProduct = Product::factory()->create([
            'name' => 'Variation Product',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => true,
        ]);

        // Index products
        Product::whereIn('id', [$mainProduct->id, $variationProduct->id])->searchable();

        // Wait for indexing
        sleep(1);

        // Search for products
        $request = new Request(['q' => 'Product', 'per_page' => 10]);
        $results = $this->elasticsearchService->searchProducts($request);

        $this->assertGreaterThan(0, $results->total());
        $this->assertTrue($results->contains('id', $mainProduct->id));
        $this->assertFalse($results->contains('id', $variationProduct->id));
    }

    protected function tearDown(): void
    {
        // Clean up search index
        try {
            Product::removeAllFromSearch();
        } catch (\Exception $e) {
            // Ignore cleanup errors
        }

        parent::tearDown();
    }
}
