<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Search Engine
    |--------------------------------------------------------------------------
    |
    | This option controls the default search connection that gets used while
    | using Laravel Scout. This connection is used when syncing all models
    | to the search service. You should adjust this based on your needs.
    |
    */

    'driver' => env('SCOUT_DRIVER', 'elastic'),

    /*
    |--------------------------------------------------------------------------
    | Index Prefix
    |--------------------------------------------------------------------------
    |
    | Here you may specify a prefix that will be applied to all search index
    | names used by Scout. This prefix may be useful if you have multiple
    | "tenants" or applications sharing the same search infrastructure.
    |
    */

    'prefix' => env('SCOUT_PREFIX', ''),

    /*
    |--------------------------------------------------------------------------
    | Queue Data Syncing
    |--------------------------------------------------------------------------
    |
    | This option allows you to control if the operations that sync your data
    | with your search engines are queued. When this is set to "true" then
    | all automatic data syncing will get queued for better performance.
    |
    */

    'queue' => env('SCOUT_QUEUE', false),

    /*
    |--------------------------------------------------------------------------
    | Chunk Sizes
    |--------------------------------------------------------------------------
    |
    | These options allow you to control the maximum chunk size when you are
    | mass importing data into the search engine. This allows you to fine
    | tune each of these chunk sizes based on the power of your machines.
    |
    */

    'chunk' => [
        'searchable' => 500,
        'unsearchable' => 500,
    ],

    /*
    |--------------------------------------------------------------------------
    | Soft Deletes
    |--------------------------------------------------------------------------
    |
    | This option allows you to control whether to keep soft deleted records
    | in your search indexes. Maintaining soft deleted records can be useful
    | if your application still needs to search for the records after they
    | are deleted.
    |
    */

    'soft_delete' => false,

    /*
    |--------------------------------------------------------------------------
    | Identify User
    |--------------------------------------------------------------------------
    |
    | This option allows you to control whether to notify the search engine
    | of the user performing the search. This is sometimes useful if the
    | search engine supports any analytics based on this application.
    |
    */

    'identify' => env('SCOUT_IDENTIFY', false),

    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Elasticsearch settings. You will need to
    | configure the host and port of your Elasticsearch instance. You may
    | also configure authentication if your instance requires it.
    |
    */

    'elasticsearch' => [
        'index' => env('ELASTICSEARCH_INDEX', 'products'),

        'hosts' => [
            env('ELASTICSEARCH_SCHEME', 'http') . '://' .
                env('ELASTICSEARCH_HOST', 'localhost') . ':' .
                env('ELASTICSEARCH_PORT', 9200),
        ],

        'retries' => 1,

        'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', false),
        'ssl_cert' => env('ELASTICSEARCH_SSL_CERT'),
        'ssl_key' => env('ELASTICSEARCH_SSL_KEY'),
        'ssl_ca' => env('ELASTICSEARCH_SSL_CA'),

        'username' => env('ELASTICSEARCH_USERNAME'),
        'password' => env('ELASTICSEARCH_PASSWORD'),
        'cloud_id' => env('ELASTICSEARCH_CLOUD_ID'),
        'api_key' => env('ELASTICSEARCH_API_KEY'),

        /*
        |--------------------------------------------------------------------------
        | Index Settings and Mappings
        |--------------------------------------------------------------------------
        */
        'indices' => [
            'settings' => [
                'default' => [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                    'analysis' => [
                        'analyzer' => [
                            'standard' => [
                                'type' => 'standard',
                                'stopwords' => '_english_',
                            ],
                        ],
                    ],
                ],
            ],

            'mappings' => [
                'default' => [
                    'properties' => [
                        'id' => ['type' => 'keyword'],
                        'name' => [
                            'type' => 'text',
                            'analyzer' => 'standard',
                            'fields' => [
                                'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                            ],
                        ],
                        'description' => ['type' => 'text', 'analyzer' => 'standard'],
                        'content' => ['type' => 'text', 'analyzer' => 'standard'],
                        'sku' => ['type' => 'keyword'],
                        'price' => ['type' => 'double'],
                        'sale_price' => ['type' => 'double'],
                        'brand_id' => ['type' => 'integer'],
                        'brand_name' => [
                            'type' => 'text',
                            'analyzer' => 'standard',
                            'fields' => [
                                'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                            ],
                        ],
                        'categories' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'name' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                                'slug' => ['type' => 'keyword'],
                            ],
                        ],
                        'tags' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'name' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                            ],
                        ],
                        'attributes' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'title' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                                'value' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                            ],
                        ],
                        'stock_status' => ['type' => 'keyword'],
                        'status' => ['type' => 'keyword'],
                        'is_featured' => ['type' => 'boolean'],
                        'is_variation' => ['type' => 'boolean'],
                        'created_at' => ['type' => 'date'],
                        'updated_at' => ['type' => 'date'],
                    ],
                ],
            ],
        ],
    ],



    'elastic' => [
        'index' => env('ELASTICSEARCH_INDEX', 'products'),

        'hosts' => [
            env('ELASTICSEARCH_SCHEME', 'http') . '://' .
                env('ELASTICSEARCH_HOST', 'localhost') . ':' .
                env('ELASTICSEARCH_PORT', 9200),
        ],

        'retries' => 1,

        'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', false),
        'ssl_cert' => env('ELASTICSEARCH_SSL_CERT'),
        'ssl_key' => env('ELASTICSEARCH_SSL_KEY'),
        'ssl_ca' => env('ELASTICSEARCH_SSL_CA'),

        'username' => env('ELASTICSEARCH_USERNAME'),
        'password' => env('ELASTICSEARCH_PASSWORD'),
        'cloud_id' => env('ELASTICSEARCH_CLOUD_ID'),
        'api_key' => env('ELASTICSEARCH_API_KEY'),

        /*
        |--------------------------------------------------------------------------
        | Index Settings and Mappings
        |--------------------------------------------------------------------------
        */
        'indices' => [
            'settings' => [
                'default' => [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                    'analysis' => [
                        'analyzer' => [
                            'standard' => [
                                'type' => 'standard',
                                'stopwords' => '_english_',
                            ],
                        ],
                    ],
                ],
            ],

            'mappings' => [
                'default' => [
                    'properties' => [
                        'id' => ['type' => 'keyword'],
                        'name' => [
                            'type' => 'text',
                            'analyzer' => 'standard',
                            'fields' => [
                                'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                            ],
                        ],
                        'description' => ['type' => 'text', 'analyzer' => 'standard'],
                        'content' => ['type' => 'text', 'analyzer' => 'standard'],
                        'sku' => ['type' => 'keyword'],
                        'price' => ['type' => 'double'],
                        'sale_price' => ['type' => 'double'],
                        'brand_id' => ['type' => 'integer'],
                        'brand_name' => [
                            'type' => 'text',
                            'analyzer' => 'standard',
                            'fields' => [
                                'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                            ],
                        ],
                        'categories' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'name' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                                'slug' => ['type' => 'keyword'],
                            ],
                        ],
                        'tags' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'name' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                            ],
                        ],
                        'attributes' => [
                            'type' => 'nested',
                            'properties' => [
                                'id' => ['type' => 'integer'],
                                'title' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                                'value' => [
                                    'type' => 'text',
                                    'analyzer' => 'standard',
                                    'fields' => [
                                        'keyword' => ['type' => 'keyword', 'ignore_above' => 256],
                                    ],
                                ],
                            ],
                        ],
                        'stock_status' => ['type' => 'keyword'],
                        'status' => ['type' => 'keyword'],
                        'is_featured' => ['type' => 'boolean'],
                        'is_variation' => ['type' => 'boolean'],
                        'created_at' => ['type' => 'date'],
                        'updated_at' => ['type' => 'date'],
                    ],
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Algolia Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Algolia settings. Algolia is a cloud hosted
    | search engine which works great with Scout out of the box. Just plug
    | in your application ID and admin API key to get started searching.
    |
    */

    'algolia' => [
        'id' => env('ALGOLIA_APP_ID', ''),
        'secret' => env('ALGOLIA_SECRET', ''),
    ],

];
