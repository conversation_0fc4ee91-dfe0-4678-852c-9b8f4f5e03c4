<?php

namespace Botble\Ecommerce\Http\Resources;

use Botble\Ecommerce\Models\Product;
use Botble\Media\Facades\RvMedia;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Product
 */
class ProductResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'content' => $this->content,
            'sku' => $this->sku,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'original_price' => $this->original_price,
            'front_sale_price' => $this->front_sale_price,
            'price_with_taxes' => $this->price_with_taxes,
            'formatted_price' => format_price($this->price),
            'formatted_sale_price' => format_price($this->sale_price),
            'formatted_price_with_taxes' => format_price($this->price_with_taxes),
            'sale_percentage' => get_sale_percentage($this->price, $this->front_sale_price),
            'image' => $this->image ? RvMedia::url($this->image) : null,
            'image_with_sizes' => $this->image_with_sizes,
            'images' => collect($this->images)->map(function ($image) {
                return RvMedia::url($image);
            })->toArray(),
            'brand' => new BrandResource($this->whenLoaded('brand')),
            'categories' => ProductCategoryResource::collection($this->whenLoaded('categories')),
            'tags' => $this->whenLoaded('tags', function () {
                return $this->tags->map(function ($tag) {
                    return [
                        'id' => $tag->id,
                        'name' => $tag->name,
                        'slug' => $tag->slug,
                    ];
                });
            }),
            'attributes' => $this->whenLoaded('variationAttributeSwatchesForProductList', function () {
                return $this->variationAttributeSwatchesForProductList->map(function ($attribute) {
                    return [
                        'id' => $attribute->id,
                        'title' => $attribute->title,
                        'slug' => $attribute->slug,
                        'value' => $attribute->pivot->value ?? '',
                    ];
                });
            }),
            'variations' => ProductVariationResource::collection($this->whenLoaded('variations')),
            'stock_status' => $this->stock_status?->value,
            'stock_status_label' => $this->stock_status_label,
            'stock_status_html' => $this->stock_status_html,
            'quantity' => $this->quantity,
            'is_out_of_stock' => $this->isOutOfStock(),
            'with_storehouse_management' => $this->with_storehouse_management,
            'allow_checkout_when_out_of_stock' => $this->allow_checkout_when_out_of_stock,
            'is_featured' => $this->is_featured,
            'is_variation' => $this->is_variation,
            'status' => $this->status?->value,
            'weight' => $this->weight,
            'height' => $this->height,
            'wide' => $this->wide,
            'length' => $this->length,
            'barcode' => $this->barcode,
            'cost_per_item' => $this->cost_per_item,
            'minimum_order_quantity' => $this->minimum_order_quantity,
            'maximum_order_quantity' => $this->maximum_order_quantity,
            'views' => $this->views,
            'url' => $this->url,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
