<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Http\Resources\ProductResource;
use Botble\Ecommerce\Http\Resources\ProductVariationResource;
use Botble\Ecommerce\Services\ElasticsearchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ProductSearchController extends BaseController
{
    public function __construct(
        protected ElasticsearchService $elasticsearchService
    ) {
    }

    /**
     * Search products using Elasticsearch
     *
     * @group Product Search
     * @bodyParam q string The search query. Example: "laptop"
     * @bodyParam page integer The page number for pagination. Example: 1
     * @bodyParam per_page integer Number of items per page (max 100). Example: 12
     * @bodyParam sort_by string Sort products by: relevance, price_asc, price_desc, name_asc, name_desc, newest, oldest, featured. Example: "price_asc"
     * @bodyParam min_price number Minimum price filter. Example: 100
     * @bodyParam max_price number Maximum price filter. Example: 1000
     * @bodyParam brands array Brand IDs to filter by. Example: [1, 2, 3]
     * @bodyParam categories array Category IDs to filter by. Example: [1, 2, 3]
     * @bodyParam tags array Tag IDs to filter by. Example: [1, 2, 3]
     * @bodyParam attributes object Attribute filters as key-value pairs. Example: {"1": ["red", "blue"], "2": ["large"]}
     * @bodyParam in_stock boolean Filter only products in stock. Example: true
     * @bodyParam featured boolean Filter only featured products. Example: true
     */
    public function searchProducts(Request $request): AnonymousResourceCollection
    {
        $request->validate([
            'q' => 'nullable|string|max:255',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|in:relevance,price_asc,price_desc,name_asc,name_desc,newest,oldest,featured',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'brands' => 'nullable|array',
            'brands.*' => 'integer|exists:ec_brands,id',
            'categories' => 'nullable|array',
            'categories.*' => 'integer|exists:ec_product_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'integer|exists:ec_product_tags,id',
            'attributes' => 'nullable|array',
            'in_stock' => 'nullable|boolean',
            'featured' => 'nullable|boolean',
        ]);

        try {
            $results = $this->elasticsearchService->searchProducts($request);

            return ProductResource::collection($results)->additional([
                'meta' => [
                    'search_query' => $request->input('q', ''),
                    'total' => $results->total(),
                    'per_page' => $results->perPage(),
                    'current_page' => $results->currentPage(),
                    'last_page' => $results->lastPage(),
                    'from' => $results->firstItem(),
                    'to' => $results->lastItem(),
                ],
                'filters_applied' => [
                    'query' => $request->input('q'),
                    'sort_by' => $request->input('sort_by', 'relevance'),
                    'min_price' => $request->input('min_price'),
                    'max_price' => $request->input('max_price'),
                    'brands' => $request->input('brands', []),
                    'categories' => $request->input('categories', []),
                    'tags' => $request->input('tags', []),
                    'attributes' => $request->input('attributes', []),
                    'in_stock' => $request->input('in_stock', false),
                    'featured' => $request->input('featured', false),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage('Search failed: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Search product variations using Elasticsearch
     *
     * @group Product Search
     * @bodyParam q string The search query. Example: "red shirt"
     * @bodyParam page integer The page number for pagination. Example: 1
     * @bodyParam per_page integer Number of items per page (max 100). Example: 12
     * @bodyParam sort_by string Sort variations by: relevance, price_asc, price_desc, name_asc, name_desc, newest, oldest. Example: "price_asc"
     * @bodyParam configurable_product_id integer Filter variations by configurable product ID. Example: 123
     */
    public function searchVariations(Request $request): AnonymousResourceCollection
    {
        $request->validate([
            'q' => 'nullable|string|max:255',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|in:relevance,price_asc,price_desc,name_asc,name_desc,newest,oldest',
            'configurable_product_id' => 'nullable|integer|exists:ec_products,id',
        ]);

        try {
            $results = $this->elasticsearchService->searchProductVariations($request);

            return ProductVariationResource::collection($results)->additional([
                'meta' => [
                    'search_query' => $request->input('q', ''),
                    'total' => $results->total(),
                    'per_page' => $results->perPage(),
                    'current_page' => $results->currentPage(),
                    'last_page' => $results->lastPage(),
                    'from' => $results->firstItem(),
                    'to' => $results->lastItem(),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage('Variation search failed: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Get search suggestions
     *
     * @group Product Search
     * @bodyParam q string required The search query for suggestions. Example: "lap"
     * @bodyParam limit integer Number of suggestions to return (max 20). Example: 10
     */
    public function suggestions(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2|max:255',
            'limit' => 'nullable|integer|min:1|max:20',
        ]);

        try {
            $suggestions = $this->elasticsearchService->getSearchSuggestions(
                $request->input('q'),
                $request->input('limit', 10)
            );

            return $this->httpResponse()
                ->setData([
                    'suggestions' => $suggestions,
                    'query' => $request->input('q'),
                ])
                ->toApiResponse();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage('Suggestions failed: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Get search aggregations for filters
     *
     * @group Product Search
     * @bodyParam q string The search query to get aggregations for. Example: "laptop"
     */
    public function aggregations(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'nullable|string|max:255',
        ]);

        try {
            $aggregations = $this->elasticsearchService->getAggregations(
                $request->input('q', '')
            );

            return $this->httpResponse()
                ->setData([
                    'aggregations' => $aggregations,
                    'query' => $request->input('q', ''),
                ])
                ->toApiResponse();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage('Aggregations failed: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Health check for Elasticsearch connection
     *
     * @group Product Search
     */
    public function healthCheck(): JsonResponse
    {
        try {
            // Try to perform a simple search to test Elasticsearch connection
            $testSearch = $this->elasticsearchService->searchProducts(
                new Request(['q' => '', 'per_page' => 1])
            );

            return $this->httpResponse()
                ->setData([
                    'status' => 'healthy',
                    'elasticsearch' => 'connected',
                    'message' => 'Search service is working properly',
                ])
                ->toApiResponse();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage('Search service is not available: ' . $e->getMessage())
                ->setData([
                    'status' => 'unhealthy',
                    'elasticsearch' => 'disconnected',
                ])
                ->toApiResponse();
        }
    }
}
