<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use Botble\Ecommerce\Models\Product;
use Illuminate\Support\Facades\Http;

class MakePostsFeedsInFacebook extends Command
{
    protected $signature = 'facebook:post-products';
    protected $description = 'Post product feeds to Facebook with text';

    protected $appId = '1299625374913508';

    protected $appSecret = 'e3909d158839dbd2a6f6f6d2f21e17d2';

    public function handle()
    {
        $products = Product::limit(5)->get();

        foreach ($products as $product) {
            $message = "Check out our product: {$product->name}\n{$product->description}\nPrice: {$product->price}";

            // Facebook Page Access Token and Page ID
            $accessToken = '1299625374913508|eMTy7cn9NG33Dk8UM2wtCwv11Xg';
              $accessToken = 'EAASeAJzQEZBQBO6yitancOa8ZA6IMtstMerZCGBmDb9QsPPvWAFnKaX2sSxarhLIaCcGkEFNRqiZA6wV5NYhMLW3qXZBmC9CBehZAf3JW3EduJrWAD4IXNd0F8ZBxup2r2E5Mup7PxLKMqZBkb1gbmLM9RVw1pBxUopQWQYHJA0gr6IZBnz2HvrbXaw7ulEV5IxIM2PDbZBuRcs1ZAibJgWmKqEK6xp3zcpIvFbooCZA9oo4SgQ3Dv0l';
            $pageId = 'karaz.store.jo';

            $response = Http::post("https://graph.facebook.com/{$pageId}/feed", [
                'message' => $message,
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                $this->info("Posted: {$product->name}");
            } else {
                $this->error("Failed to post: {$product->name}");
            }
            dd($response->body()); // Debugging line to check the response
        }
    }
}