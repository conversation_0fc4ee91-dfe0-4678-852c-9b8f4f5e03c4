<?php

namespace App\Console\Commands;


use Bo<PERSON>ble\Ecommerce\Models\ProductCategory;
use <PERSON><PERSON>ble\Slug\Models\Slug;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FixedCategoriesProduct extends Command
{
    protected $signature = 'fixed:categories-product';
    protected $description = 'Fixed categories product';

    public function handle()
    {
       ProductCategory::query()->chunk(400, function ($categories)  {
        $categories->each(function ($category) {
            $this->info('Processing category: ' . $category->name);
            $localized = DB::table('ec_product_categories_translations')->select('*')->where('ec_product_categories_id', $category->id)->first();
            if(!$localized){
                DB::table('ec_product_categories_translations')->insert([
                    'ec_product_categories_id' => $category->id,
                    'lang_code' => 'en_US',
                    'name' => $category->name,
                    'description' => $category->description,
                ]);
            }
            Slug::updateOrCreate(
                    ['reference_id' => $category->id, 'reference_type' => ProductCategory::class],
                    ['key' => Str::slug($category->name), 'prefix' => 'product-categories','reference_id' => $category->id, 'reference_type' => ProductCategory::class]
                );
            });
        });
    }
}