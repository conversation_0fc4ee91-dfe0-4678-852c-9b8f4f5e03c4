<?php

namespace Tests\Feature;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Ecommerce\Models\Brand;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\Ecommerce\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductSearchApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Skip if Elasticsearch is not available
        if (!$this->isElasticsearchAvailable()) {
            $this->markTestSkipped('Elasticsearch is not available');
        }
    }

    protected function isElasticsearchAvailable(): bool
    {
        try {
            $response = $this->getJson('/api/v1/products/search/health');
            return $response->status() === 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    /** @test */
    public function it_can_search_products_via_api()
    {
        // Create test product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'This is a test product',
            'price' => 99.99,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index product
        $product->searchable();
        sleep(1); // Wait for indexing

        // Search via API
        $response = $this->postJson('/api/v1/products/search', [
            'q' => 'Test Product',
            'per_page' => 10,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'description',
                                'price',
                                'sale_price',
                                'sku',
                                'status',
                                'brand',
                                'categories',
                                'tags',
                                'images',
                                'stock_status',
                                'is_featured',
                                'created_at',
                                'updated_at',
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total',
                        'last_page',
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertGreaterThan(0, $response->json('data.total'));
    }

    /** @test */
    public function it_validates_search_parameters()
    {
        // Test with invalid per_page
        $response = $this->postJson('/api/v1/products/search', [
            'q' => 'test',
            'per_page' => 101, // Max is 100
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['per_page']);

        // Test with invalid sort_by
        $response = $this->postJson('/api/v1/products/search', [
            'q' => 'test',
            'sort_by' => 'invalid_sort',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['sort_by']);

        // Test with invalid price range
        $response = $this->postJson('/api/v1/products/search', [
            'q' => 'test',
            'min_price' => 100,
            'max_price' => 50, // max_price should be greater than min_price
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['max_price']);
    }

    /** @test */
    public function it_can_filter_products_by_price_range_via_api()
    {
        // Create products with different prices
        $cheapProduct = Product::factory()->create([
            'name' => 'Cheap Product',
            'price' => 25.00,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $expensiveProduct = Product::factory()->create([
            'name' => 'Expensive Product',
            'price' => 250.00,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$cheapProduct->id, $expensiveProduct->id])->searchable();
        sleep(1);

        // Search with price filter
        $response = $this->postJson('/api/v1/products/search', [
            'q' => '',
            'min_price' => 100,
            'max_price' => 300,
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
        
        $products = collect($response->json('data.data'));
        $this->assertTrue($products->contains('id', $expensiveProduct->id));
        $this->assertFalse($products->contains('id', $cheapProduct->id));
    }

    /** @test */
    public function it_can_filter_products_by_brand_via_api()
    {
        // Create brands
        $brand1 = Brand::factory()->create(['name' => 'Brand A']);
        $brand2 = Brand::factory()->create(['name' => 'Brand B']);

        // Create products
        $product1 = Product::factory()->create([
            'name' => 'Product A',
            'brand_id' => $brand1->id,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        $product2 = Product::factory()->create([
            'name' => 'Product B',
            'brand_id' => $brand2->id,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index products
        Product::whereIn('id', [$product1->id, $product2->id])->searchable();
        sleep(1);

        // Search with brand filter
        $response = $this->postJson('/api/v1/products/search', [
            'q' => '',
            'brands' => [$brand1->id],
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
        
        $products = collect($response->json('data.data'));
        $this->assertTrue($products->contains('id', $product1->id));
        $this->assertFalse($products->contains('id', $product2->id));
    }

    /** @test */
    public function it_can_sort_products_via_api()
    {
        // Create products with different prices
        $products = collect([
            Product::factory()->create([
                'name' => 'Product A',
                'price' => 100,
                'status' => BaseStatusEnum::PUBLISHED,
                'is_variation' => false,
            ]),
            Product::factory()->create([
                'name' => 'Product B',
                'price' => 50,
                'status' => BaseStatusEnum::PUBLISHED,
                'is_variation' => false,
            ]),
            Product::factory()->create([
                'name' => 'Product C',
                'price' => 150,
                'status' => BaseStatusEnum::PUBLISHED,
                'is_variation' => false,
            ]),
        ]);

        // Index products
        Product::whereIn('id', $products->pluck('id'))->searchable();
        sleep(1);

        // Search with price ascending sort
        $response = $this->postJson('/api/v1/products/search', [
            'q' => '',
            'sort_by' => 'price_asc',
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
        
        $resultPrices = collect($response->json('data.data'))->pluck('price')->toArray();
        $sortedPrices = collect($resultPrices)->sort()->values()->toArray();
        $this->assertEquals($sortedPrices, $resultPrices);
    }

    /** @test */
    public function it_can_get_search_suggestions_via_api()
    {
        // Create test product
        $product = Product::factory()->create([
            'name' => 'iPhone 15 Pro',
            'sku' => 'IPHONE15',
            'status' => BaseStatusEnum::PUBLISHED,
            'is_variation' => false,
        ]);

        // Index product
        $product->searchable();
        sleep(1);

        // Get suggestions
        $response = $this->getJson('/api/v1/products/search/suggestions?q=iPhone&limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'sku',
                            'price',
                        ]
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertGreaterThan(0, count($response->json('data')));
    }

    /** @test */
    public function it_can_check_elasticsearch_health_via_api()
    {
        $response = $this->getJson('/api/v1/products/search/health');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'elasticsearch_status',
                        'indices_status',
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('healthy', $response->json('data.elasticsearch_status'));
    }

    /** @test */
    public function it_handles_empty_search_query()
    {
        $response = $this->postJson('/api/v1/products/search', [
            'q' => '',
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function it_handles_search_with_no_results()
    {
        $response = $this->postJson('/api/v1/products/search', [
            'q' => 'nonexistentproduct12345',
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
        $this->assertEquals(0, $response->json('data.total'));
        $this->assertEmpty($response->json('data.data'));
    }

    protected function tearDown(): void
    {
        // Clean up search index
        try {
            Product::removeAllFromSearch();
        } catch (\Exception $e) {
            // Ignore cleanup errors
        }

        parent::tearDown();
    }
}
