# Elasticsearch Integration for Product Listing

## Overview

This implementation integrates Elasticsearch with the `/products` route to provide faster and more efficient product search and filtering capabilities. The integration maintains backward compatibility with the existing database-based approach as a fallback.

## Changes Made

### 1. PublicProductController Updates

**File:** `platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicProductController.php`

- Added `ElasticsearchService` dependency injection
- Modified `getProducts()` method to use Elasticsearch when available
- Added `shouldUseElasticsearch()` helper method to determine when to use Elasticsearch
- Implemented fallback to database search if Elasticsearch fails
- Fixed PHP 7.4 compatibility issues (removed union types and trailing commas)

### 2. ElasticsearchService Enhancements

**File:** `platform/plugins/ecommerce/src/Services/ElasticsearchService.php`

- Enhanced `searchProducts()` method to handle all request parameters from the original `GetProductService`
- Added support for multiple parameter naming conventions (`per_page`, `num`, `per-page`)
- Added support for multiple sort parameter names (`sort_by`, `sort-by`)
- Enhanced `applyFilters()` method with support for:
  - Collections (single and multiple)
  - Price ranges
  - Enhanced category, brand, and tag filtering
  - Attribute filtering
- Enhanced `applySorting()` method with additional sort options:
  - `date_asc`/`date_desc`
  - `rating_asc`/`rating_desc`
  - Default ordering for non-search queries
- Improved handling of empty queries (uses `*` for match-all)

### 3. Product Model Updates

**File:** `platform/plugins/ecommerce/src/Models/Product.php`

- Enhanced `toSearchableArray()` method to include:
  - Collections data for filtering
  - Order field for default sorting
  - Proper relationship loading checks

## Configuration

The integration uses the existing Elasticsearch configuration in `.env`:

```env
SCOUT_DRIVER=elasticsearch
ELASTICSEARCH_HOSTS=localhost:9200
ELASTICSEARCH_INDEX=products
```

## How It Works

### 1. Route Processing

When a request comes to the `/products` route:

1. The controller checks if Elasticsearch should be used via `shouldUseElasticsearch()`
2. Elasticsearch is used if:
   - `SCOUT_DRIVER=elasticsearch` is configured
   - The request contains search queries or filters that benefit from Elasticsearch
3. If Elasticsearch fails, it automatically falls back to the database approach

### 2. Parameter Mapping

The service maps all existing parameters to Elasticsearch queries:

- **Search:** `q` parameter → Elasticsearch query
- **Pagination:** `page`, `num`, `per_page`, `per-page` → Elasticsearch pagination
- **Sorting:** `sort-by`, `sort_by` → Elasticsearch sorting
- **Filters:** `categories`, `brands`, `tags`, `collections`, `price_ranges`, `attributes` → Elasticsearch filters

### 3. Fallback Mechanism

If Elasticsearch is unavailable or fails:
- The system automatically falls back to the original `GetProductService`
- Users experience no interruption in service
- All existing functionality remains intact

## Testing

### Manual Testing

Once PHP 8.1+ is available, you can test the integration:

1. **Basic Product Listing:**
   ```
   GET /products
   ```

2. **Search with Query:**
   ```
   GET /products?q=laptop&sort-by=price_asc
   ```

3. **Filtered Search:**
   ```
   GET /products?q=phone&categories[]=1&brands[]=2&min_price=100&max_price=500
   ```

4. **AJAX Requests:**
   ```
   GET /products?categories[]=1&brands[]=2 (with AJAX headers)
   ```

### Automated Testing

Run the existing test suite:

```bash
php artisan test tests/Feature/ElasticsearchProductSearchTest.php
php artisan test tests/Unit/ElasticsearchServiceTest.php
```

## Benefits

1. **Performance:** Elasticsearch provides faster search and filtering for large product catalogs
2. **Scalability:** Better handling of complex queries and large datasets
3. **Relevance:** Improved search relevance scoring
4. **Flexibility:** Support for advanced search features like fuzzy matching and aggregations
5. **Backward Compatibility:** Seamless fallback to database queries

## Requirements

- PHP 8.1+ (for union types and other modern PHP features)
- Elasticsearch server running and accessible
- Laravel Scout with Elasticsearch driver configured
- Products indexed in Elasticsearch

## Indexing Products

To index existing products in Elasticsearch:

```bash
php artisan scout:import "Botble\Ecommerce\Models\Product"
```

## Monitoring

The integration includes error handling and logging. Monitor your logs for:
- Elasticsearch connection issues
- Query failures
- Fallback usage

## Future Enhancements

1. **Aggregations:** Implement filter aggregations for dynamic filter counts
2. **Auto-complete:** Add search suggestions and auto-complete functionality
3. **Analytics:** Track search queries and popular filters
4. **Caching:** Add result caching for frequently accessed queries
